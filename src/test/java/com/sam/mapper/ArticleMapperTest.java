package com.sam.mapper;

import com.sam.MyBlogApplication;
import com.sam.entity.Article;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest(classes = MyBlogApplication.class)
class ArticleMapperTest {

    @Autowired
    private ArticleMapper articleMapper;

    @Test
    void selectList() {
        assertNotNull(articleMapper.selectList(null));
        assertNotNull(articleMapper.selectList(new Article()));
        assertNotNull(articleMapper.selectList(new Article(){{
            setTitle("test");
        }}));
    }

    @Test
    void insert() {
    }

    @Test
    void selectById() {
    }

    @Test
    void update() {
    }

    @Test
    void delete() {
    }
}