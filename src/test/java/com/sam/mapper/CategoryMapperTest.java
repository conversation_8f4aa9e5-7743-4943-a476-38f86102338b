package com.sam.mapper;

import com.sam.MyBlogApplication;
import com.sam.entity.Category;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest(classes = MyBlogApplication.class)
class CategoryMapperTest {

    @Autowired
    private CategoryMapper categoryMapper;

    @Test
    void selectAll() {
        categoryMapper.selectAll().forEach(System.out::println);
    }

    @Test
    void selectByCondition() {
        categoryMapper.selectByCondition(null).forEach(System.out::println);
        categoryMapper.selectByCondition(new Category()).forEach(System.out::println);
    }

    @Test
    void insert() {

    }

    @Test
    void update() {
    }

    @Test
    void delete() {
    }

    @Test
    void selectById() {
    }
}