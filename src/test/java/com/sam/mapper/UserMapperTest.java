package com.sam.mapper;

import com.sam.MyBlogApplication;
import com.sam.entity.User;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest(classes = MyBlogApplication.class)
class UserMapperTest {

    @Autowired
    private UserMapper userMapper;

    @Test
    void testLoginUser_withCorrectCredentials_shouldReturnUser() {
        // 使用常量或变量来存储测试数据
        final String username = "jack";
        final String password = "123456";

        User user = userMapper.loginUser(username);

        // 使用更具体的断言
        assertNotNull(user, "用户名/密码错误");
    }

//    @Test
//    void register() {
//        User user = new User();
//        user.setUsername("test");
//        user.setPassword("123456");
//        user.setSex("男");
//        user.setPhone("12345678901");
//        user.setAvatar("test");
//        user.setIntroduction("test");
//        int n = userMapper.insert(user);
//        assertEquals(1, n);
//    }

    @Test
    void selectById() {
        User user = userMapper.selectById(1L);
        assertNotNull(user);
        System.out.println(user);
    }

    @Test
    void update() {
        User user = new User();
        user.setUid(6L);
        user.setUsername("testasdf");
        user.setPassword("123456");
        user.setSex("男");
        user.setPhone("12345671111");
        user.setAvatar("test");
        user.setIntroduction("test");
        int n = userMapper.update(user);
        assertEquals(1, n);
    }

}