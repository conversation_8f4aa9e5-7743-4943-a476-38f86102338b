package com.sam.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigInteger;

/**
 * <AUTHOR>
 * @version 1.0
 * 博客种类
 * @date 2025-09-02 18:05
 */
@Data
public class Category implements Serializable {
    private Long cid;
    private String cname;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String createTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String updateTime;
}
