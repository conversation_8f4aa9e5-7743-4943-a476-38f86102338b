package com.sam.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigInteger;

/**
 * <AUTHOR>
 * @version 1.0
 * 物件(文章实体类)
 * @date 2025-09-02 18:05
 */
@Data
public class Article implements Serializable {
    private Long id;
    private String title;
    private String content;
    private Long cid;
    private Long uid;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String createTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String updateTime;
}
