package com.sam.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigInteger;

/**
 * <AUTHOR>
 * @version 1.0
 * 用户实体类
 * @date 2025-09-02 18:06
 */
@Data
public class User implements Serializable {
    private Long uid;
    private String username;
    private String password;
    private String sex;
    private String phone;
    private String avatar;
    private String introduction;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String createTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String updateTime;
}
