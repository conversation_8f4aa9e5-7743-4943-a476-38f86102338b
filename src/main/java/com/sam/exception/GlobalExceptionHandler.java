package com.sam.exception;

import com.sam.util.Result;
import com.sam.util.ResultCodeEnum;
import io.swagger.v3.oas.annotations.Hidden;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@Hidden
@RestControllerAdvice // 表示这个类是异常处理类
@Slf4j
public class GlobalExceptionHandler {
    @ExceptionHandler(SystemException.class)
    public Result doSystemException(SystemException ex){
        ex.printStackTrace();
        return Result.fail(ex.getCode(),ex.getMessage());
    }

    @ExceptionHandler(BusinessException.class)
    public Result doBusinessException(BusinessException ex){
        ex.printStackTrace();
        return Result.fail(ex.getCode(),ex.getMessage());
    }

    //除了自定义的异常处理器，保留对Exception类型的异常处理，用于处理非预期的异常
    @ExceptionHandler(Exception.class)
    public Result doOtherException(Exception ex){
        ex.printStackTrace();
        return Result.fail(ResultCodeEnum.FAIL.getCode(),
                ResultCodeEnum.FAIL.getMessage()+":"+ex.getMessage());
    }
}
