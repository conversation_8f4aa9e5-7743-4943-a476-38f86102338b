package com.sam.mapper;

import com.sam.entity.Article;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ArticleMapper {

    //查询所有文章
    List<Article> selectList(@Param("vo") Article article);

    //添加文章
    int insert(Article article);

    //根据id查询文章
    Article selectById(Long id);

    //修改文章
    int update(Article article);

    //删除文章
    int delete(Long id);

}
