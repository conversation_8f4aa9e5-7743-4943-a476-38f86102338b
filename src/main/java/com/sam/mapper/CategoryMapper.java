package com.sam.mapper;

import com.sam.entity.Category;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface CategoryMapper {
    //查询所有分类
    List<Category> selectAll();

    //条件查询
    List<Category> selectByCondition(Category category);

    //添加
    int insert(Category category);

    //修改
    int update(Category category);

    //删除
    int delete(Long id);

    //根据id查询
    Category selectById(Long id);



}
