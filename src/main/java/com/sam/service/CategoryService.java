package com.sam.service;

import com.sam.entity.Category;
import com.sam.util.Result;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025-09-02 18:41
 */

public interface CategoryService {

    //查询所有
    Result list();

    //分页
    Result queryListPage(Integer current, Integer limit, Category category);

    //save
    Result save(Category category);

    //update
    Result update(Category category);

    //delete
    Result delete(Long id);

    //根据id查询
    Result findById(Long id);
}
