package com.sam.service;

import com.sam.entity.Article;
import com.sam.util.Result;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025-09-02 18:41
 */

public interface ArticleService {

    //查询所有
    Result<List<Article>> list(Article article);

    //添加文章
    Result insert(Article article);

    //根据id查询文章
    Result selectById(Long id);

    //修改文章
    Result update(Article article);

    //删除文章
    Result delete(Long id);



}
