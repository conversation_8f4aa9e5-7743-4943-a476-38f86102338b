package com.sam.service.impl;

import com.sam.entity.User;
import com.sam.mapper.UserMapper;
import com.sam.service.UserService;
import com.sam.util.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025-09-02 18:56
 */
@Service
public class UserServiceImpl implements UserService {

    @Autowired
    private UserMapper userMapper;

    //登陆
    @Override
    public Result<User> login(User user) {
        User selectUser = userMapper.loginUser(String.valueOf(user.getUsername())
        );
        if(selectUser != null){
            String inputPassword = user.getPassword();
            String dbPassword = selectUser.getPassword();
            if(inputPassword.equals(dbPassword)){
                return Result.ok(selectUser);
            }else{
                return Result.fail(500,"用户名或密码错误");
            }
        }
        return Result.fail(500,"用户名或密码错误");
    }

    @Override
    public Result register(User user) {
        //根据用户名查询，如过存在相同的就不能注册
        User selectUser = userMapper.loginUser(user.getUsername());
        if(selectUser == null){
            //register
            int n = userMapper.insert(user);
            if(n > 0){
                return Result.ok();
            }else{
                return Result.fail(500,"注册失败");
            }
        }
        return Result.fail(500,"用户名已存在");
    }

    @Override
    public Result findUser(Long uid) {
        User user = userMapper.selectById(uid);
        if(user != null){
            return Result.ok(user);
        }
        return Result.fail(500,"用户不存在");
    }

    @Override
    public Result modifyUser(User user) {
        int n = userMapper.update(user);
        if(n > 0){
            return Result.ok();
        }
        return Result.fail(500,"修改失败");
    }
}
