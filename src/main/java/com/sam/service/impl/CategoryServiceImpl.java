package com.sam.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.sam.entity.Category;
import com.sam.mapper.CategoryMapper;
import com.sam.service.CategoryService;
import com.sam.util.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025-09-02 18:56
 */
@Service
public class CategoryServiceImpl implements CategoryService {

    @Autowired
    private CategoryMapper categoryMapper;

    @Override
    public Result list() {
        List<Category> list = categoryMapper.selectAll();
        return Result.ok(list);
    }

    @Override
    public Result queryListPage(Integer current, Integer limit, Category category) {
        PageHelper.startPage(current, limit);
        //执行操作
        List<Category> categories = categoryMapper.selectByCondition(category);
        //封装分页结果
        PageInfo<Category> pageInfo = new PageInfo<>(categories);
        //返回结果
        return Result.ok(pageInfo);
    }

    @Override
    public Result save(Category category) {
        int n = categoryMapper.insert(category);
        if (n > 0) {
            return Result.ok();
        }
        return Result.fail();
    }

    @Override
    public Result update(Category category) {
        int n = categoryMapper.update(category);
        if (n > 0) {
            return Result.ok();
        }
        return Result.fail();
    }

    @Override
    public Result delete(Long id) {
        int n = categoryMapper.delete(id);
        if (n > 0) {
            return Result.ok();
        }
        return Result.fail();
    }

    @Override
    public Result findById(Long id) {
        Category category = categoryMapper.selectById(id);
        return category != null ? Result.ok(category) : Result.fail();
    }
}
