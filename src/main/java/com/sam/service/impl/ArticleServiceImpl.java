package com.sam.service.impl;

import com.sam.entity.Article;
import com.sam.mapper.ArticleMapper;
import com.sam.service.ArticleService;
import com.sam.util.Result;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025-09-02 18:56
 */
@Service
public class ArticleServiceImpl implements ArticleService {

    @Autowired
    private ArticleMapper articleMapper;

    //查询所有文章
    @Override
    public Result<List<Article>> list(Article article) {
        List<Article> list = articleMapper.selectList(article);
        return Result.ok(list);
    }


    //添加文章
    @Override
    public Result insert(Article article) {
        int n = articleMapper.insert(article);
        if (n > 0) {
            return Result.ok();
        }
        return Result.fail();
    }

    @Override
    public Result selectById(Long id) {
        Article article = articleMapper.selectById(id);
        return article != null ? Result.ok(article) : Result.fail();
    }

    @Override
    public Result update(Article article) {
        int n = articleMapper.update(article);
        if (n > 0) {
            return Result.ok();
        }
        return Result.fail();

    }

    @Override
    public Result delete(Long id) {
        int n = articleMapper.delete(id);
        if (n > 0) {
            return Result.ok();
        }
        return Result.fail();
    }
}
