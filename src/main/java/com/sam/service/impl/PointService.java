package com.sam.service.impl;

import com.sam.event.LoginSuccessEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * 积分服务
 */
@Slf4j
@Service
public class PointService {
    @Async // 设置异步处理事件
    //监听登录成功事件，当指定的事件发生时，此方法自动被调用
    @EventListener(LoginSuccessEvent.class)
    public void handleLoginSuccess(LoginSuccessEvent event){
        String username = event.getUsername();
        log.info("监听器监听到，用户{}登录成功，给用户增加积分！",username);
        // 具体的加积分的逻辑
        addUserPoint(username);
    }

    @Async
    public void addUserPoint(String username){
        // 发送优惠券的具体实现
        log.info("已经给{}增加2点积分!", username);
    }
}
