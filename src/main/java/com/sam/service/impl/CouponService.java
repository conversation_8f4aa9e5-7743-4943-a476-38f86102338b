package com.sam.service.impl;

import com.sam.event.LoginSuccessEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * 优惠卷服务
 */
@Slf4j
@Service
public class CouponService {

    @Async // 设置异步处理事件
    //监听登录成功事件，当指定的事件发生时，此方法自动被调用
    @EventListener(LoginSuccessEvent.class)
    public void handleLoginSuccess(LoginSuccessEvent event){
        String username = event.getUsername();
        log.info("监听器监听到，用户{}登录成功，可以给用户发放优惠券啦！",username);
        // 具体的发券逻辑
        sendCouponToUser(username);
    }

    @Async
    public void sendCouponToUser(String username){
        // 发送优惠券的具体实现
        log.info("优惠券已发送给用户: {}", username);
    }
}
