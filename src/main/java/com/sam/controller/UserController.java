package com.sam.controller;

import com.sam.entity.User;
import com.sam.event.LoginSuccessEvent;
import com.sam.service.UserService;
import com.sam.util.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025-09-02 18:37
 */
@RestController
@RequestMapping("/users")
@Slf4j
@CrossOrigin
public class UserController {

    @Autowired
    private UserService userService;

    @Autowired
    private ApplicationEventPublisher publisher;

    @Operation(summary = "用户登录", description = "根据用户名和密码进行登录验证")
    @PostMapping("/login")
    public Result loginUser(@Parameter(description = "用户登录信息") @RequestBody User user) {

        log.info("检查登录参数: {}", user);
        log.info("登录成功, 发布事件");
        Result<User> result = userService.login(user);
        if (result.getCode() == 200) {
            LoginSuccessEvent event = new LoginSuccessEvent(user.getUsername());
            publisher.publishEvent(event);
        }
        return userService.login(user);
    }

    @PostMapping("/register")
    public Result register(@RequestBody User user) {
        log.info("检查注册参数: {}", user);
        return userService.register(user);
    }

    //根据Id查询
    @GetMapping("/{uid}")
    public Result getUser(@PathVariable("uid") Long uid) {
        log.info("检查查询参数: {}", uid);
        return userService.findUser(uid);
    }

    @PutMapping
    public Result modifyUser(@RequestBody User user) {
        log.info("检查修改参数: {}", user);
        return userService.modifyUser(user);
    }




}
