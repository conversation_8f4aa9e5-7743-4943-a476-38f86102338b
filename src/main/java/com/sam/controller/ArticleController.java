package com.sam.controller;

import com.sam.entity.Article;
import com.sam.service.ArticleService;
import com.sam.util.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025-09-02 18:37
 */
@Tag(name = "文章接口")
@Slf4j
@RestController
@RequestMapping("/articles")
@CrossOrigin
public class ArticleController {

    @Autowired
    private ArticleService articleService;

    @Operation(summary = "查询所有文章", description = "根据条件查询文章")
    @PostMapping("/queryList")
    public Result articleList(@Parameter(description = "文章信息")@RequestBody(required = false) Article article) {
        log.info("查询所有文章");
        return articleService.list(article);
    }

    @Operation(summary = "添加文章", description = "添加文章")
    @PostMapping("/add")
    public Result addArticle(@Parameter(description = "文章信息")@RequestBody Article article) {
        log.info("添加文章{}", article);
        return articleService.insert(article);
    }

    @Operation(summary = "根据ID查询文章", description = "根据ID查询文章")
    @Parameter(name = "id", description = "文章ID",required = true)
    @GetMapping("/{id}")
    public Result getArticle(@PathVariable("id") Long id) {
        log.info("根据ID查询文章{}", id);
        return articleService.selectById(id);
    }

    @Operation(summary = "修改文章", description = "修改文章")
    @PutMapping
    public Result updateArticle(@Parameter(description = "文章信息")@RequestBody Article article) {
        log.info("修改文章{}", article);
        return articleService.update(article);
    }

    @Operation(summary = "删除文章", description = "删除文章")
    @Parameter(name = "id", description = "文章ID",required = true)
    @DeleteMapping("/{id}")
    public Result deleteArticle(@PathVariable("id") Long id) {
        log.info("删除文章{}", id);
        return articleService.delete(id);
    }
}
