package com.sam.controller;

import com.sam.entity.Category;
import com.sam.service.CategoryService;
import com.sam.util.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025-09-02 18:37
 */
@Tag(name = "分类管理")
@RestController
@RequestMapping("/categories")
@CrossOrigin
@Slf4j
public class CategoryController {

    @Autowired
    private CategoryService categoryService;

    @Operation(summary = "查询所有分类", description = "查询所有分类")
    @GetMapping
    public Result articleList() {
        log.info("查询所有分类");
        return categoryService.list();
    }

    @Operation(summary = "获取分页列表", description = "获取分页列表")
    @Parameters({
            @Parameter(name = "current", description = "当前页码",required = true),
            @Parameter(name = "limit", description = "每页显示的记录数",required = true),
            @Parameter(name = "category", description = "查询条件",required = false)
    })
    @PostMapping("queryPage/{current}/{limit}")
    public Result queryListPage(@PathVariable("current") Integer current,
                               @PathVariable("limit") Integer limit,
                               @RequestBody(required = false) Category category) {
        log.info("获取分页列表");
        return categoryService.queryListPage(current, limit, category);
    }

    @Operation(summary = "添加分类", description = "添加分类")
    @PostMapping
    public Result addCategory(@Parameter(name = "category", description = "分类信息",required = true) @RequestBody Category category) {
        log.info("添加分类{}", category);
        return categoryService.save(category);
    }

    //根据ID查询
    @Operation(summary = "根据ID查询", description = "根据ID查询")
    @Parameter(name = "id", description = "分类ID",required = true)
    @GetMapping("/{id}")
    public Result getCategory(@PathVariable("id") Long id) {
        log.info("根据ID查询分类{}", id);
        return categoryService.findById(id);
    }


    @Operation(summary = "修改分类", description = "修改分类")
    @PutMapping
    public Result updateCategory(@Parameter(name = "category", description = "分类信息",required = true) @RequestBody Category category) {
        log.info("修改分类{}", category);
        return categoryService.update(category);
    }

    @Operation(summary = "删除分类", description = "删除分类")
    @Parameter(name = "id", description = "分类ID",required = true)
    @DeleteMapping("/{id}")
    public Result deleteCategory(@PathVariable("id") Long id) {
        log.info("删除分类{}", id);
        return categoryService.delete(id);
    }
}
