server:
  # 端口号设置
  port: 8080
  # 项目根路径
  servlet:
    context-path: /

spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    # --- 核心连接信息，这是修正的关键 ---
    url: **********************************************************************************************************************************
    username: root
    password: '00000000'
    driver-class-name: com.mysql.cj.jdbc.Driver
    # --- Druid的特定配置 ---
    druid:
      initialSize: 5
      minIdle: 5
      maxActive: 20
      maxWait: 60000

  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8

mybatis:
  configuration:
    auto-mapping-behavior: full
    # 强烈建议开启驼峰命名自动转换
    map-underscore-to-camel-case: true
    lazy-loading-enabled: true
    aggressive-lazy-loading: false
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
  type-aliases-package: com.sam.entity
  mapper-locations: classpath:/mapper/*.xml

pagehelper:
  helperDialect: mysql
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql

springdoc:
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  api-docs:
    path: /v3/api-docs
  info:
    title: 博客系统API文档
    description: 这是一个博客系统的后端API接口文档
    version: v1.0.0
    contact:
      name: sam
      email: <EMAIL>
      url: http://localhost:8080/
  group-configs:
    - group: 'default'
      paths-to-match: '/**'
      packages-to-scan: com.sam.controller

knife4j:
  enable: true
  setting:
    language: zh_cn

logging:
  level:
    com.sam.mapper: debug
    com.sam.service: debug
    com.sam.controller: debug