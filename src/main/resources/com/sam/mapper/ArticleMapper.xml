<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sam.mapper.ArticleMapper">

    <!--查询所有文章-->
    <select id="selectList" resultType="com.sam.entity.Article">
        select * from article
        <where>
            <if test="vo.title != null and vo.title != ''">
                and title like concat('%',#{vo.title},'%')
            </if>
            <if test="vo.content != null and vo.content != ''">
                and content like concat('%',#{vo.content},'%')
            </if>
            <if test="vo.cid != null">
                and cid = #{vo.cid}
            </if>
            <if test="vo.uid != null">
                and uid = #{vo.uid}
            </if>
        </where>
    </select>
    <!--添加文章-->
    <insert id="insert" parameterType="com.sam.entity.Article">
        insert into article(title,content,cid,uid)
        values(#{title},#{content},#{cid},#{uid})
    </insert>
    <!--根据id查询文章-->
    <select id="selectById" resultType="com.sam.entity.Article">
        select * from article where id = #{id}
    </select>
    <!--修改文章-->
    <update id="update" parameterType="com.sam.entity.Article">
        update article
        <set>
            <if test="title != null and title != ''">
                title = #{title},
            </if>
            <if test="content != null and content != ''">
                content = #{content},
            </if>
            <if test="cid != null and cid != ''">
                cid = #{cid},
            </if>
            <if test="uid != null and uid != ''">
                uid = #{uid},
            </if>
            update_time = now()
        </set>
        where id = #{id}
    </update>
    <!--删除文章-->
    <delete id="delete" parameterType="long">
        delete from article where id = #{id}
    </delete>
</mapper>