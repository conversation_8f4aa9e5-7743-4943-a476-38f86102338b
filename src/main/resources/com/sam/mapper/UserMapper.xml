<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sam.mapper.UserMapper">

    <!-- 用户登录 -->
    <select id="loginUser" resultType="User">
        select * from user where username = #{username}
    </select>

    <!-- 用户注册 -->
    <insert id="insert">
        insert into user (username, password, sex, phone, avatar, introduction)
        values (#{username}, #{password}, #{sex}, #{phone}, #{avatar}, #{introduction})
    </insert>

    <!-- 查询用户 -->
    <select id="selectById" resultType="com.sam.entity.User">
        select * from user where uid = #{uid}
    </select>

    <!-- 修改用户信息 -->
    <update id="update">
        update user set username = #{username}, password = #{password}, sex = #{sex}, phone = #{phone}, avatar = #{avatar}, introduction = #{introduction} where uid = #{uid}
    </update>

</mapper>