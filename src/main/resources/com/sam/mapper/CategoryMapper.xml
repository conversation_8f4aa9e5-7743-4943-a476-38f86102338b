<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sam.mapper.CategoryMapper">

    <!--查询所有-->
    <select id="selectAll" resultType="Category">
        select cid,cname,create_time,update_time from category
        order by cid asc
    </select>

    <!--条件查询-->
    <select id="selectByCondition" parameterType="Category" resultType="Category">
        select cid,cname,create_time,update_time from category
        <where>
            <if test="cname != null and cname != ''">
                and cname like concat('%',#{cname},'%')
            </if>
        </where>
        order by cid asc
    </select>

    <!--插入分类-->
    <insert id="insert" parameterType="category">
            insert into category(cname,create_time,update_time)
            values(#{cname},now(),now())
    </insert>

    <!--修改分类(动态SQL)-->
    <update id="update" parameterType="category">
            update category
            <set>
                <if test="cname != null and cname != ''">
                    cname = #{cname},
                </if>
                update_time = now()
            </set>
            where cid = #{cid}
    </update>

    <!--删除分类-->
    <delete id="delete" parameterType="long">
            delete from category where cid = #{cid}
    </delete>

    <!--根据id查询-->
    <select id="selectById" parameterType="long" resultType="Category">
            select cid,cname,create_time,update_time from category where cid = #{cid}
    </select>

</mapper>